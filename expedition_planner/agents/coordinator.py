"""
Main coordinator agent for orchestrating the expedition document processing workflow.
"""

import logging
import uuid
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from .data_extractor import DataExtractionAgent
from .document_organizer import DocumentOrganizerAgent
from .json_generator import JSONGeneratorAgent
from .pattern_analyzer import PatternAnalysisAgent

logger = logging.getLogger(__name__)


class ExpeditionCoordinator:
    """Main coordinator agent that orchestrates the entire expedition document processing workflow."""

    def __init__(self):
        """Initialize the expedition coordinator with all sub-agents."""
        self.session_id = str(uuid.uuid4())
        self.document_organizer = DocumentOrganizerAgent()
        self.data_extractor = DataExtractionAgent()
        self.pattern_analyzer = PatternAnalysisAgent()
        self.json_generator = JSONGeneratorAgent()

        logger.info(
            f"Expedition coordinator initialized with session ID: {self.session_id}"
        )

    def process_expedition_documents(
        self,
        documents_directory: str,
        output_directory: str,
        expedition_name: str = "Expedition",
        enable_analysis: bool = False,  # Default to False - focus on JSON generation
    ) -> Dict[str, Any]:
        """
        Complete workflow for processing expedition documents with focus on JSON generation.

        This workflow prioritizes creating clean, consistent JSON templates from multiple
        documents. Pattern analysis is optional and should typically be run separately
        after collecting sufficient JSON templates from multiple operations.

        Args:
            documents_directory: Directory containing expedition documents
            output_directory: Directory to save processed results
            expedition_name: Name of the expedition
            enable_analysis: Whether to run pattern analysis (default: False)

        Returns:
            Dictionary containing complete processing results
        """
        # Initialize workflow state early to ensure it's available in exception handler
        workflow_state = {
            "session_id": self.session_id,
            "expedition_name": expedition_name,
            "start_time": datetime.now().isoformat(),
            "documents_directory": documents_directory,
            "output_directory": output_directory,
            "status": "processing",
            "steps": {},
            "processing_focus": "json_generation" if not enable_analysis else "full_processing",
        }

        try:
            logger.info("Starting expedition document processing workflow")
            logger.info(f"Documents: {documents_directory}")
            logger.info(f"Output: {output_directory}")
            logger.info(f"Processing focus: {'JSON Generation Only' if not enable_analysis else 'Full Processing with Analysis'}")

            # Create output directory
            output_path = Path(output_directory)
            output_path.mkdir(parents=True, exist_ok=True)

            # Check if documents exist
            docs_path = Path(documents_directory)
            if not docs_path.exists() or not any(docs_path.iterdir()):
                workflow_state["status"] = "failed"
                workflow_state["error"] = (
                    "No documents found in the specified directory"
                )
                return workflow_state

            # Step 1: Document Organization and Location Detection
            logger.info("Step 1: Organizing documents and detecting locations")

            # Enhanced location detection from expedition name and document content
            location = self._detect_location_from_context(expedition_name, docs_path)

            workflow_state["steps"]["organization"] = {
                "success": True,
                "method": "enhanced_detection",
                "detected_location": location,
                "document_count": len(list(docs_path.glob("*"))),
                "focus": "json_template_generation"
            }

            # Step 2: Enhanced Data Extraction with Terminology Handling
            logger.info("Step 2: Extracting data with enhanced terminology handling")

            # Enhanced extraction that handles varying terminology
            extraction_results = self._extract_with_terminology_handling(docs_path, location)
            workflow_state["steps"]["extraction"] = extraction_results

            successful_extractions = {
                loc: result for loc, result in extraction_results.items()
                if result.get("success")
            }

            # Step 3: Pattern Analysis (conditional - typically skipped for individual operations)
            if enable_analysis:
                logger.info("Step 3: Analyzing operational patterns (Note: Limited effectiveness with single operation)")

                # Warn about limited pattern analysis effectiveness
                logger.warning("Pattern analysis on single operation has limited effectiveness. Consider running pattern analysis on multiple JSON files instead.")

                # Combine all extracted data for pattern analysis
                combined_data = self._combine_extraction_results(successful_extractions)

                pattern_analysis_result = (
                    self.pattern_analyzer.analyze_operational_patterns(combined_data)
                )
                workflow_state["steps"]["pattern_analysis"] = pattern_analysis_result

                # Generate pattern analysis report
                pattern_report = ""
                if pattern_analysis_result.get("success"):
                    pattern_report = (
                        self.pattern_analyzer.create_pattern_analysis_report(
                            combined_data
                        )
                    )
            else:
                logger.info("Step 3: Skipping pattern analysis (recommended for individual operations)")
                pattern_analysis_result = {
                    "success": True,
                    "skipped": True,
                    "reason": "Pattern analysis skipped - run separately on multiple JSON files for better results",
                    "recommendation": "Generate JSON templates first, then run pattern analysis on multiple operations"
                }
                workflow_state["steps"]["pattern_analysis"] = pattern_analysis_result
                pattern_report = "Pattern analysis was skipped. For meaningful patterns, analyze multiple operation JSON files together."

            # Step 4: JSON Generation
            logger.info("Step 4: Generating JSON templates")
            json_generation_results = {}

            for location, extraction_result in successful_extractions.items():
                if extraction_result.get("success"):
                    logger.info(f"Generating JSON templates for location: {location}")

                    consolidated_data = extraction_result.get("consolidated_data", {})

                    # Use consolidated_outputs directory for JSON files
                    generation_result = self.json_generator.generate_json_templates(
                        consolidated_data, pattern_report, "consolidated_outputs"
                    )
                    json_generation_results[location] = generation_result

            workflow_state["steps"]["json_generation"] = json_generation_results

            # Step 5: Final Summary and Validation
            logger.info("Step 5: Creating final summary")

            workflow_state["status"] = "completed"
            workflow_state["end_time"] = datetime.now().isoformat()
            workflow_state["summary"] = self._create_workflow_summary(workflow_state)

            # Save workflow state
            self._save_workflow_state(workflow_state, output_path)

            logger.info(
                "Expedition document processing workflow completed successfully"
            )
            return workflow_state

        except Exception as e:
            logger.error(f"Error in expedition processing workflow: {e}")
            workflow_state["status"] = "failed"
            workflow_state["error"] = str(e)
            workflow_state["end_time"] = datetime.now().isoformat()
            return workflow_state

    def process_single_location(
        self,
        document_paths: List[str],
        location: str,
        output_directory: str,
        enable_analysis: bool = True,
    ) -> Dict[str, Any]:
        """
        Process documents for a single location.

        Args:
            document_paths: List of document file paths
            location: Location name
            output_directory: Output directory

        Returns:
            Dictionary containing processing results for the location
        """
        try:
            logger.info(f"Processing single location: {location}")

            # Create output directory
            output_path = Path(output_directory)
            output_path.mkdir(parents=True, exist_ok=True)

            # Extract data
            extraction_result = self.data_extractor.extract_from_document_group(
                document_paths, location
            )

            if not extraction_result.get("success"):
                return {
                    "success": False,
                    "location": location,
                    "error": f"Data extraction failed: {extraction_result.get('error')}",
                }

            # Preserve original rich data before pattern analysis
            consolidated_data = extraction_result.get("consolidated_data", {})
            original_rich_data = self._preserve_rich_data(consolidated_data)

            # Analyze patterns (conditional) - but don't let it reduce the data
            if enable_analysis:
                # Create a copy for pattern analysis to avoid data modification
                pattern_analysis_data = self._prepare_data_for_pattern_analysis(consolidated_data)

                pattern_analysis = self.pattern_analyzer.analyze_operational_patterns(
                    {"days": [pattern_analysis_data]}
                )

                # Generate pattern report
                pattern_report = ""
                if pattern_analysis.get("success"):
                    pattern_report = (
                        self.pattern_analyzer.create_pattern_analysis_report(
                            {"days": [pattern_analysis_data]}
                        )
                    )
            else:
                pattern_analysis = {
                    "success": True,
                    "skipped": True,
                    "reason": "Pattern analysis disabled by user",
                }
                pattern_report = "Pattern analysis was skipped."

            # Generate JSON templates using the ORIGINAL rich data, not the reduced data
            # Use consolidated_outputs directory for JSON files
            json_result = self.json_generator.generate_json_templates(
                original_rich_data, pattern_report, "consolidated_outputs"
            )

            return {
                "success": True,
                "location": location,
                "extraction": extraction_result,
                "pattern_analysis": pattern_analysis,
                "json_generation": json_result,
                "pattern_report": pattern_report,
            }

        except Exception as e:
            logger.error(f"Error processing single location {location}: {e}")
            return {"success": False, "location": location, "error": str(e)}

    def get_processing_status(self) -> Dict[str, Any]:
        """Get current processing status."""
        return {
            "session_id": self.session_id,
            "agents_status": {
                "document_organizer": "ready",
                "data_extractor": "ready",
                "pattern_analyzer": "ready",
                "json_generator": "ready",
            },
            "capabilities": [
                "Document organization by location/operation site",
                "Structured data extraction using Docling",
                "Operational pattern analysis",
                "JSON template generation",
                "Multiple operation type support (AM-only, PM-only, combined)",
            ],
        }

    def _combine_extraction_results(
        self, extraction_results: Dict[str, Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Combine extraction results from multiple locations."""
        combined_days = []

        for location, result in extraction_results.items():
            if result.get("success"):
                consolidated_data = result.get("consolidated_data", {})
                if consolidated_data:
                    # Ensure location is set
                    consolidated_data["location"] = location
                    combined_days.append(consolidated_data)

        return {"days": combined_days}

    def _create_workflow_summary(
        self, workflow_state: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create a summary of the workflow execution."""
        steps = workflow_state.get("steps", {})

        # Count successful operations
        organization = steps.get("organization", {})
        extraction = steps.get("extraction", {})
        pattern_analysis = steps.get("pattern_analysis", {})
        json_generation = steps.get("json_generation", {})

        total_documents = organization.get("total_documents", 0)
        total_groups = organization.get("total_groups", 0)

        successful_extractions = sum(
            1 for result in extraction.values() if result.get("success")
        )

        total_json_files = 0
        for result in json_generation.values():
            if result.get("success"):
                total_json_files += result.get("total_files", 0)

        return {
            "expedition_name": workflow_state.get("expedition_name"),
            "processing_time": self._calculate_processing_time(workflow_state),
            "documents_processed": total_documents,
            "location_groups": total_groups,
            "successful_extractions": successful_extractions,
            "json_files_generated": total_json_files,
            "pattern_analysis_completed": pattern_analysis.get("success", False),
            "status": workflow_state.get("status"),
            "output_directory": workflow_state.get("output_directory"),
        }

    def _calculate_processing_time(self, workflow_state: Dict[str, Any]) -> str:
        """Calculate total processing time."""
        try:
            start_time = datetime.fromisoformat(workflow_state.get("start_time", ""))
            end_time = datetime.fromisoformat(workflow_state.get("end_time", ""))
            duration = end_time - start_time
            return str(duration)
        except Exception:
            return "Unknown"

    def _detect_location_from_context(self, expedition_name: str, docs_path: Path) -> str:
        """Enhanced location detection from expedition name and document content."""
        # Expanded location mapping with more locations
        location_mapping = {
            "lacepede": "The Lacepedes",
            "king george": "King George River",
            "mitchell": "Mitchell Falls",
            "montgomery": "Montgomery Reef",
            "horizontal": "Horizontal Falls",
            "kimberley": "Kimberley Coast",
            "talbot": "Talbot Bay",
            "buccaneer": "Buccaneer Archipelago",
            "raft point": "Raft Point",
            "hunter": "Hunter River",
            "bigge": "Bigge Island",
            "careening": "Careening Bay",
            "prince regent": "Prince Regent River",
            "berkeley": "Berkeley River",
            "adele": "Adele Island",
            "jar island": "Jar Island",
            "vansittart": "Vansittart Bay",
            "swift bay": "Swift Bay",
            "koolama": "Koolama Bay",
            "camden": "Camden Sound",
            "doubtful": "Doubtful Bay"
        }

        # Check expedition name first
        expedition_lower = expedition_name.lower()
        for key, location in location_mapping.items():
            if key in expedition_lower:
                logger.info(f"Location detected from expedition name: {location}")
                return location

        # Try to detect from document names
        for doc_file in docs_path.glob("*"):
            doc_name_lower = doc_file.name.lower()
            for key, location in location_mapping.items():
                if key in doc_name_lower:
                    logger.info(f"Location detected from document name: {location}")
                    return location
                    
        # Try to extract from document content
        try:
            # Get first few documents
            doc_files = list(docs_path.glob("*"))[:3]
            
            for doc_file in doc_files:
                if doc_file.is_file():
                    # Use text extractor to get content
                    from ..core.text_extractor import create_text_extractor
                    extractor = create_text_extractor()
                    text = extractor.extract_text(str(doc_file))
                    
                    if text:
                        # Check for known locations in text
                        for key, location in location_mapping.items():
                            if key in text.lower():
                                logger.info(f"Location detected from document content: {location}")
                                return location
                                
                        # Try to extract location using regex patterns
                        import re
                        location_patterns = [
                            r'(?:at|in|location[:\s]+)([A-Z][a-z]+ [A-Z][a-z]+(?: [A-Z][a-z]+)?)',  # Location: King George River
                            r'(?:site|destination)[:\s]+([A-Z][a-z]+ [A-Z][a-z]+(?: [A-Z][a-z]+)?)',  # Site: Montgomery Reef
                            r'(?:^|\n)([A-Z][a-z]+ [A-Z][a-z]+(?: [A-Z][a-z]+)?)(?:\n|:)'  # Location at start of line or paragraph
                        ]
                        
                        for pattern in location_patterns:
                            location_match = re.search(pattern, text)
                            if location_match:
                                extracted_location = location_match.group(1)
                                # Verify it's not just a date or common phrase
                                if not re.search(r'\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\b', extracted_location) and \
                                   not re.search(r'\b(?:Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday)\b', extracted_location):
                                    logger.info(f"Location extracted from text pattern: {extracted_location}")
                                    return extracted_location
        except Exception as e:
            logger.error(f"Error extracting location from documents: {e}")

        # Default fallback
        logger.info("Using default location: The Lacepedes")
        return "The Lacepedes"

    def _extract_with_terminology_handling(self, docs_path: Path, location: str) -> Dict[str, Any]:
        """Enhanced extraction that handles varying terminology across documents."""
        try:
            logger.info(f"Starting real data extraction for {location}")

            # Get all document files
            doc_files = [str(f) for f in docs_path.glob("*") if f.is_file()]

            if not doc_files:
                logger.warning(f"No document files found in {docs_path}")
                return {
                    location: {
                        "success": False,
                        "error": "No document files found",
                        "location": location
                    }
                }

            logger.info(f"Found {len(doc_files)} documents to process")

            # Use the data extractor to process the documents
            extraction_result = self.data_extractor.extract_from_document_group(
                doc_files, location
            )

            if extraction_result.get("success"):
                logger.info(f"Successfully extracted data for {location}")
                return {
                    location: extraction_result
                }
            else:
                logger.error(f"Data extraction failed for {location}: {extraction_result.get('error')}")
                return {
                    location: {
                        "success": False,
                        "error": extraction_result.get("error", "Unknown extraction error"),
                        "location": location
                    }
                }

        except Exception as e:
            logger.error(f"Error in enhanced extraction: {e}")
            return {
                location: {
                    "success": False,
                    "error": str(e),
                    "location": location
                }
            }

    def _save_workflow_state(self, workflow_state: Dict[str, Any], output_path: Path):
        """Save workflow state to output directory."""
        try:
            state_file = output_path / f"workflow_state_{self.session_id}.json"

            with open(state_file, "w", encoding="utf-8") as f:
                import json

                json.dump(workflow_state, f, indent=2, default=str)

            logger.info(f"Workflow state saved to: {state_file}")

        except Exception as e:
            logger.error(f"Error saving workflow state: {e}")

    def create_processing_report(self, workflow_state: Dict[str, Any]) -> str:
        """Create a human-readable processing report."""
        summary = workflow_state.get("summary", {})

        report = f"""
EXPEDITION DOCUMENT PROCESSING REPORT
====================================

Expedition: {summary.get("expedition_name", "Unknown")}
Session ID: {workflow_state.get("session_id")}
Status: {summary.get("status", "Unknown")}
Processing Time: {summary.get("processing_time", "Unknown")}

PROCESSING SUMMARY:
- Documents Processed: {summary.get("documents_processed", 0)}
- Location Groups: {summary.get("location_groups", 0)}
- Successful Extractions: {summary.get("successful_extractions", 0)}
- JSON Files Generated: {summary.get("json_files_generated", 0)}
- Pattern Analysis: {"Completed" if summary.get("pattern_analysis_completed") else "Failed"}

OUTPUT LOCATION:
{summary.get("output_directory", "Unknown")}

WORKFLOW STEPS:
"""

        steps = workflow_state.get("steps", {})
        for step_name, step_result in steps.items():
            status = "✓ Success" if step_result.get("success") else "✗ Failed"
            report += f"\n{step_name.replace('_', ' ').title()}: {status}"

        if workflow_state.get("status") == "failed":
            report += f"\n\nERROR: {workflow_state.get('error', 'Unknown error')}"

        return report

    def _preserve_rich_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Preserve rich data using memory-efficient shallow copy with shared references."""

        # Create shallow copy for most fields to avoid memory overhead
        preserved_data = {
            'date': data.get('date'),
            'location': data.get('location'),
            'operation_type': data.get('operation_type'),
            'notes': data.get('notes'),
            'weather': data.get('weather')
        }

        # Share references for large collections (don't copy)
        if 'groups' in data:
            preserved_data['groups'] = data['groups']  # Share reference
        if 'schedule' in data:
            preserved_data['schedule'] = data['schedule']  # Share reference
        if 'equipment' in data:
            preserved_data['equipment'] = data['equipment']  # Share reference
        if 'personnel' in data:
            preserved_data['personnel'] = data['personnel']  # Share reference
        if 'tides' in data:
            preserved_data['tides'] = data['tides']  # Share reference

        # Ensure all rich data fields are preserved
        if "groups" in preserved_data and preserved_data["groups"]:
            logger.info(f"Preserving {len(preserved_data['groups'])} groups in rich data")

        if "schedule" in preserved_data and preserved_data["schedule"]:
            logger.info(f"Preserving {len(preserved_data['schedule'])} schedule events in rich data")

        if "equipment" in preserved_data and preserved_data["equipment"]:
            logger.info(f"Preserving equipment data: {preserved_data['equipment']}")

        if "personnel" in preserved_data and preserved_data["personnel"]:
            logger.info(f"Preserving personnel data: {preserved_data['personnel']}")

        # Add metadata to track data preservation
        preserved_data["_data_preservation"] = {
            "preserved_at": datetime.now().isoformat(),
            "original_keys": list(data.keys()),
            "preservation_method": "deep_copy",
            "rich_data_indicators": {
                "has_groups": bool(preserved_data.get("groups")),
                "has_schedule": bool(preserved_data.get("schedule")),
                "has_equipment": bool(preserved_data.get("equipment")),
                "has_personnel": bool(preserved_data.get("personnel")),
                "has_tides": bool(preserved_data.get("tides")),
                "has_weather": bool(preserved_data.get("weather"))
            }
        }

        return preserved_data

    def _prepare_data_for_pattern_analysis(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare data for pattern analysis using memory-efficient shallow copy."""

        # Create shallow copy for pattern analysis to avoid memory overhead
        analysis_data = {
            'date': data.get('date'),
            'location': data.get('location'),
            'operation_type': data.get('operation_type'),
            'notes': data.get('notes'),
            'weather': data.get('weather')
        }

        # Share references for collections needed by pattern analysis
        if 'groups' in data:
            analysis_data['groups'] = data['groups']  # Share reference
        if 'schedule' in data:
            analysis_data['schedule'] = data['schedule']  # Share reference

        # Ensure pattern analysis gets the data it needs but doesn't reduce it
        # Add any necessary transformations for pattern analysis here

        # Add metadata for pattern analysis
        analysis_data["_analysis_preparation"] = {
            "prepared_at": datetime.now().isoformat(),
            "purpose": "pattern_analysis",
            "data_integrity_check": {
                "groups_count": len(analysis_data.get("groups", [])),
                "schedule_count": len(analysis_data.get("schedule", [])),
                "equipment_present": bool(analysis_data.get("equipment")),
                "personnel_present": bool(analysis_data.get("personnel"))
            }
        }

        return analysis_data
