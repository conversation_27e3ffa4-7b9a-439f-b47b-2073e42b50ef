/**
 * Override for the JSON files display functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Wait for the agent interface to initialize
    setTimeout(() => {
        // Override the displayJsonFiles function
        if (window.expeditionAgent) {
            const originalDisplayJsonFiles = window.expeditionAgent.displayJsonFiles;
            
            window.expeditionAgent.displayJsonFiles = function(jsonFiles) {
                console.log('Overriding displayJsonFiles with:', jsonFiles);
                
                const jsonFilesContainer = document.getElementById('json-files');
                if (!jsonFilesContainer) return;
                
                jsonFilesContainer.innerHTML = "<h3>Generated JSON Templates</h3>";
                
                // Check if we have the new flat structure
                if (jsonFiles && typeof jsonFiles === 'object') {
                    const fileEntries = Object.entries(jsonFiles);
                    
                    if (fileEntries.length > 0) {
                        // Create a container for all files
                        const filesContainer = document.createElement('div');
                        filesContainer.className = 'json-file-list';
                        
                        // Add each file as a download link
                        fileEntries.forEach(([fileName, filePath]) => {
                            const fileLink = document.createElement('a');
                            fileLink.href = `/api/download/${encodeURIComponent(filePath)}`;
                            fileLink.className = 'file-link';
                            fileLink.setAttribute('download', '');
                            
                            // Format the display name
                            let displayName = fileName;
                            if (fileName.includes('_')) {
                                // Try to extract location and operation type
                                const parts = fileName.split('_');
                                if (parts.length >= 2) {
                                    const location = parts[0].replace(/_/g, ' ');
                                    const operationType = parts.slice(1).join(' ').replace(/_/g, ' ');
                                    displayName = `${location} - ${operationType}`;
                                }
                            }
                            
                            fileLink.textContent = displayName;
                            
                            // Add a view button
                            const viewButton = document.createElement('button');
                            viewButton.className = 'btn btn-sm btn-outline-info ms-2';
                            viewButton.innerHTML = '<i class="fas fa-eye"></i> View';
                            viewButton.onclick = function(e) {
                                e.preventDefault();
                                // Trigger the view in the JSON preview
                                if (window.viewJsonFile) {
                                    window.viewJsonFile(filePath);
                                } else {
                                    // Fallback to fetch and display
                                    fetch(`/api/download/${encodeURIComponent(filePath)}?format=json`)
                                        .then(response => response.json())
                                        .then(data => {
                                            const jsonPreview = document.getElementById('json-preview');
                                            if (jsonPreview) {
                                                jsonPreview.textContent = JSON.stringify(data, null, 2);
                                                if (window.hljs) {
                                                    hljs.highlightElement(jsonPreview);
                                                }
                                            }
                                        });
                                }
                            };
                            
                            // Create a container for the file link and view button
                            const fileLinkContainer = document.createElement('div');
                            fileLinkContainer.className = 'file-link-container mb-2';
                            fileLinkContainer.appendChild(fileLink);
                            fileLinkContainer.appendChild(viewButton);
                            
                            filesContainer.appendChild(fileLinkContainer);
                        });
                        
                        jsonFilesContainer.appendChild(filesContainer);
                    } else {
                        jsonFilesContainer.innerHTML += "<p>No JSON files were generated.</p>";
                    }
                } else if (originalDisplayJsonFiles) {
                    // Fall back to the original function if we have the old structure
                    originalDisplayJsonFiles.call(this, jsonFiles);
                }
            };
        }
    }, 500); // Wait for the agent to initialize
    
    // Expose the viewJsonFile function globally
    window.viewJsonFile = function(filePath) {
        fetch(`/api/download/${encodeURIComponent(filePath)}?format=json`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('JSON file fetched successfully');
                const jsonPreview = document.getElementById('json-preview');
                if (jsonPreview) {
                    const jsonString = JSON.stringify(data, null, 2);
                    jsonPreview.textContent = jsonString;
                    
                    // Apply syntax highlighting if available
                    if (window.hljs) {
                        hljs.highlightElement(jsonPreview);
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching JSON file:', error);
                const jsonPreview = document.getElementById('json-preview');
                if (jsonPreview) {
                    jsonPreview.textContent = `Error loading JSON: ${error.message}`;
                }
            });
    };
});