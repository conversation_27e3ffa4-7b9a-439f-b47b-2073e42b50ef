/**
 * JSON Preview functionality for the Expedition Planner
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const jsonPreviewElement = document.getElementById('json-preview');
    const toggleJsonViewBtn = document.getElementById('toggle-json-view');
    const copyJsonBtn = document.getElementById('copy-json');
    const viewJsonBtn = document.getElementById('view-json');
    
    // State
    let currentJsonData = null;
    let isFormatted = true;
    let currentFilePath = null;
    
    // Event listeners
    if (toggleJsonViewBtn) {
        toggleJsonViewBtn.addEventListener('click', toggleJsonFormat);
    }
    
    if (copyJsonBtn) {
        copyJsonBtn.addEventListener('click', copyJsonToClipboard);
    }
    
    if (viewJsonBtn) {
        viewJsonBtn.addEventListener('click', viewFirstJsonFile);
    }
    
    // Socket.io event listener for processing complete
    if (typeof io !== 'undefined') {
        const socket = io();
        
        socket.on('processing_complete', function(data) {
            console.log('Processing complete received:', data);
            
            if (data.json_files && Object.keys(data.json_files).length > 0) {
                // Store the JSON files data
                currentJsonData = data.json_files;
                console.log('JSON files data:', currentJsonData);
                
                // Automatically view the first JSON file
                viewFirstJsonFile();
            } else {
                console.log('No JSON files found in the response');
                // Try to find JSON files in the output directory
                searchForJsonFiles(data);
            }
        });
    }
    
    // On page load, try to get the latest JSON file
    window.addEventListener('load', function() {
        // If we're on the results page, try to get the latest JSON
        if (jsonPreviewElement) {
            getLatestJsonFile();
        }
    });
    
    /**
     * Search for JSON files in the output directory if none were provided in the response
     */
    function searchForJsonFiles(data) {
        // Check if we have an output directory in the response
        if (data.summary && data.summary.output_directory) {
            const outputDir = data.summary.output_directory;
            console.log('Searching for JSON files in output directory:', outputDir);
            
            // Make a request to search for JSON files
            fetch('/api/search-json-files', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ output_directory: outputDir }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.json_files && Object.keys(data.json_files).length > 0) {
                    currentJsonData = data.json_files;
                    console.log('Found JSON files:', currentJsonData);
                    viewFirstJsonFile();
                } else {
                    console.log('No JSON files found in search');
                    showNoJsonFilesMessage();
                }
            })
            .catch(error => {
                console.error('Error searching for JSON files:', error);
                showNoJsonFilesMessage();
            });
        } else {
            showNoJsonFilesMessage();
        }
    }
    
    /**
     * Show a message when no JSON files are found
     */
    function showNoJsonFilesMessage() {
        if (jsonPreviewElement) {
            jsonPreviewElement.textContent = 'No JSON files were generated or found. Please check the logs for details.';
        }
    }
    
    /**
     * Toggle between formatted and compact JSON display
     */
    function toggleJsonFormat() {
        if (!jsonPreviewElement.textContent) return;
        
        isFormatted = !isFormatted;
        
        // If we have the current file path, refetch it with the new format
        if (currentFilePath) {
            fetchJsonFile(currentFilePath);
        } else {
            // Otherwise just reformat the current content
            updateJsonPreview(jsonPreviewElement.textContent);
        }
    }
    
    /**
     * Copy JSON content to clipboard
     */
    function copyJsonToClipboard() {
        if (!jsonPreviewElement.textContent) return;
        
        navigator.clipboard.writeText(jsonPreviewElement.textContent)
            .then(() => {
                // Show success message
                const originalText = copyJsonBtn.innerHTML;
                copyJsonBtn.innerHTML = '<i class="fas fa-check me-1"></i>Copied!';
                
                setTimeout(() => {
                    copyJsonBtn.innerHTML = originalText;
                }, 2000);
            })
            .catch(err => {
                console.error('Failed to copy: ', err);
            });
    }
    
    /**
     * View the first available JSON file
     */
    function viewFirstJsonFile() {
        if (!currentJsonData) {
            console.log('No JSON data available');
            return;
        }
        
        console.log('Viewing first JSON file from:', currentJsonData);
        
        // Get the first file key
        const fileKeys = Object.keys(currentJsonData);
        if (fileKeys.length === 0) {
            console.log('No JSON files found in data');
            return;
        }
        
        // Get the first file path
        const firstKey = fileKeys[0];
        const filePath = currentJsonData[firstKey];
        
        console.log('Fetching JSON file:', filePath);
        
        // Store the current file path
        currentFilePath = filePath;
        
        // Fetch and display the JSON file
        fetchJsonFile(filePath);
    }
    
    /**
     * Fetch a JSON file and display it in the preview
     */
    function fetchJsonFile(filePath) {
        // Add format=json parameter to get JSON content instead of download
        fetch(`/api/download/${encodeURIComponent(filePath)}?format=json`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('JSON file fetched successfully');
                updateJsonPreview(data);
            })
            .catch(error => {
                console.error('Error fetching JSON file:', error);
                jsonPreviewElement.textContent = `Error loading JSON: ${error.message}`;
            });
    }
    
    /**
     * Get the latest JSON file from the server
     */
    function getLatestJsonFile() {
        console.log('Getting latest JSON file');
        
        fetch('/api/get-latest-json')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    console.log('Latest JSON file:', data);
                    updateJsonPreview(data.json_data);
                    
                    // Create a simple flat JSON files structure for download links
                    currentJsonData = {
                        [data.file_name.replace('.json', '')]: data.file_path
                    };
                    
                    currentFilePath = data.file_path;
                } else {
                    console.log('No latest JSON file found');
                }
            })
            .catch(error => {
                console.error('Error getting latest JSON file:', error);
            });
    }
    
    /**
     * Update the JSON preview with the provided data
     */
    function updateJsonPreview(data) {
        if (!data) return;
        
        let jsonString;
        
        // Convert to string if it's an object
        if (typeof data === 'object') {
            jsonString = JSON.stringify(data, null, isFormatted ? 2 : 0);
        } else {
            // If it's already a string, try to parse and re-stringify for formatting
            try {
                const jsonObj = JSON.parse(data);
                jsonString = JSON.stringify(jsonObj, null, isFormatted ? 2 : 0);
            } catch (e) {
                // If parsing fails, use the original string
                jsonString = data;
            }
        }
        
        // Update the preview
        jsonPreviewElement.textContent = jsonString;
        
        // Apply syntax highlighting if available
        if (window.hljs) {
            hljs.highlightElement(jsonPreviewElement);
        }
    }
});